/*
 * Xgm003CollectiveOutputDTO01.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.dto;

import java.util.ArrayList;
import java.util.List;

import com.jast.gakuen.core.common.dto.BaseCollectiveOutputDTO;

import lombok.Getter;
import lombok.Setter;

/**
 * 学生納付金通知書（帳票出力）DTO(非同期処理)
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Getter
@Setter
public class Xgm003CollectiveOutputDTO01 extends BaseCollectiveOutputDTO {

	/**
	 * 条件
	 */
	private Xgm003ConditionDTO02 condition;

	/**
	 * 納付金リスト
	 */
	private List<Xgm003DTO02> payList = new ArrayList<>();

}
